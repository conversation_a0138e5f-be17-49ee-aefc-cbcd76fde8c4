'use client';

import { useState } from 'react';
import Navigation from '@/components/Navigation';
import AgeVerification from '@/components/AgeVerification';
import Hero from '@/components/Hero';
import ProductShowcase from '@/components/ProductShowcase';
import LocationInfo from '@/components/LocationInfo';

export default function Home() {
  const [isAgeVerified, setIsAgeVerified] = useState(false);

  const handleAgeVerified = () => {
    setIsAgeVerified(true);
  };

  return (
    <>
      <AgeVerification onVerified={handleAgeVerified} />

      {isAgeVerified && (
        <div className="min-h-screen bg-cream-100">
          <Navigation />
          <main>
            <Hero />
            <ProductShowcase />
            <LocationInfo />

            {/* Newsletter Section */}
            <section className="py-20 bg-primary-800">
              <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 className="text-3xl font-serif font-bold text-cream-50 mb-4">
                  Stay Updated with Apothecary Extracts
                </h2>
                <p className="text-xl text-cream-200 mb-8">
                  Get the latest news on new products, special offers, and cannabis education.
                </p>
                <form className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="flex-1 px-4 py-3 rounded-lg border border-cream-300 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:border-transparent"
                    required
                  />
                  <button
                    type="submit"
                    className="px-8 py-3 bg-gold-500 text-primary-800 font-semibold rounded-lg hover:bg-gold-400 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:ring-offset-2 focus:ring-offset-primary-800"
                  >
                    Subscribe
                  </button>
                </form>
                <p className="text-sm text-cream-300 mt-4">
                  We respect your privacy. Unsubscribe at any time.
                </p>
              </div>
            </section>
          </main>

          {/* Footer */}
          <footer className="bg-charcoal-800 text-cream-100 py-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                {/* Company Info */}
                <div className="col-span-1 md:col-span-2">
                  <div className="text-2xl font-serif font-bold text-cream-50 mb-4">
                    Apothecary Extracts
                  </div>
                  <p className="text-cream-300 mb-4 leading-relaxed">
                    Colorado's premier cannabis dispensary, committed to providing
                    the highest quality products and exceptional customer service.
                  </p>
                  <div className="flex space-x-4">
                    <a href="#" className="text-cream-300 hover:text-gold-400 transition-colors">
                      <span className="sr-only">Facebook</span>
                      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                      </svg>
                    </a>
                    <a href="#" className="text-cream-300 hover:text-gold-400 transition-colors">
                      <span className="sr-only">Instagram</span>
                      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z"/>
                      </svg>
                    </a>
                  </div>
                </div>

                {/* Quick Links */}
                <div>
                  <h3 className="text-lg font-semibold text-cream-50 mb-4">Quick Links</h3>
                  <ul className="space-y-2">
                    <li><a href="/products" className="text-cream-300 hover:text-gold-400 transition-colors">Products</a></li>
                    <li><a href="/locations" className="text-cream-300 hover:text-gold-400 transition-colors">Locations</a></li>
                    <li><a href="/about" className="text-cream-300 hover:text-gold-400 transition-colors">About Us</a></li>
                    <li><a href="/education" className="text-cream-300 hover:text-gold-400 transition-colors">Education</a></li>
                  </ul>
                </div>

                {/* Legal */}
                <div>
                  <h3 className="text-lg font-semibold text-cream-50 mb-4">Legal</h3>
                  <ul className="space-y-2">
                    <li><a href="/privacy" className="text-cream-300 hover:text-gold-400 transition-colors">Privacy Policy</a></li>
                    <li><a href="/terms" className="text-cream-300 hover:text-gold-400 transition-colors">Terms of Service</a></li>
                    <li><a href="/compliance" className="text-cream-300 hover:text-gold-400 transition-colors">Compliance</a></li>
                  </ul>
                </div>
              </div>

              <div className="border-t border-charcoal-600 mt-12 pt-8 text-center">
                <p className="text-cream-400 text-sm">
                  © 2025 Apothecary Extracts. All rights reserved. |
                  Licensed Cannabis Retailer | Must be 21+ to purchase
                </p>
              </div>
            </div>
          </footer>
        </div>
      )}
    </>
  );
}
      <footer className="row-start-3 flex gap-[24px] flex-wrap items-center justify-center">
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/file.svg"
            alt="File icon"
            width={16}
            height={16}
          />
          Learn
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/window.svg"
            alt="Window icon"
            width={16}
            height={16}
          />
          Examples
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/globe.svg"
            alt="Globe icon"
            width={16}
            height={16}
          />
          Go to nextjs.org →
        </a>
      </footer>
    </div>
  );
}
